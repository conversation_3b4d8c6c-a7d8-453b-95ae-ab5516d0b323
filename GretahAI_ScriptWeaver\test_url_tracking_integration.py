#!/usr/bin/env python3
"""
Test URL Tracking Integration

This script tests the complete URL tracking integration from pytest execution
to JSON storage, simulating what happens in Stage 7.
"""

import sys
import os
import json
import tempfile
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from core.junit_parser import parse_junit_xml, format_test_results_for_display
from stages.stage7 import _capture_and_store_url_tracking
from state_manager import StateManager
from core.step_data_storage import get_step_data_storage


def test_url_tracking_integration():
    """Test the complete URL tracking integration."""
    print("🔍 Testing URL Tracking Integration...")
    
    # Step 1: Parse the XML file we just generated
    xml_file = "test_url_tracking.xml"
    if not os.path.exists(xml_file):
        print(f"❌ XML file {xml_file} not found")
        return False
    
    print(f"📄 Parsing XML file: {xml_file}")
    xml_results = parse_junit_xml(xml_file)
    
    if not xml_results:
        print("❌ Failed to parse XML file")
        return False
    
    print("✅ XML file parsed successfully")
    
    # Step 2: Format results for display (this is what Stage 7 uses)
    formatted_results = format_test_results_for_display(xml_results)
    
    if not formatted_results:
        print("❌ Failed to format test results")
        return False
    
    print("✅ Test results formatted successfully")
    
    # Step 3: Check if URL tracking data is preserved
    test_details = formatted_results.get('test_details', [])
    if not test_details:
        print("❌ No test details found in formatted results")
        return False
    
    test_detail = test_details[0]
    properties = test_detail.get('properties', {})
    
    current_url = properties.get('current_url')
    url_timestamp = properties.get('url_capture_timestamp')
    
    if not current_url:
        print("❌ No current_url found in test properties")
        print(f"Available properties: {list(properties.keys())}")
        return False
    
    if not url_timestamp:
        print("❌ No url_capture_timestamp found in test properties")
        return False
    
    print(f"✅ URL tracking data found:")
    print(f"   📍 Current URL: {current_url}")
    print(f"   ⏰ Timestamp: {url_timestamp}")
    
    # Step 4: Test the Stage 7 URL tracking function
    print("\n🔧 Testing Stage 7 URL tracking function...")
    
    # Create a mock state manager
    state = StateManager()
    
    # Set up test case and step data
    state.selected_test_case = {'Test Case ID': 'TC_001'}
    state.selected_step = {'Step No': '1'}
    
    # Call the URL tracking function
    try:
        _capture_and_store_url_tracking(state, formatted_results)
        print("✅ URL tracking function executed successfully")
    except Exception as e:
        print(f"❌ URL tracking function failed: {e}")
        return False
    
    # Step 5: Verify the data was stored in JSON
    print("\n💾 Checking JSON storage...")
    
    storage = get_step_data_storage()
    step_data_result = storage.load_step_data('TC_001')
    
    if not step_data_result:
        print("❌ Failed to load step data from storage")
        return False
    
    step_data, metadata = step_data_result
    
    # Find step 1
    step_1 = None
    for step in step_data:
        if str(step.get('step_no', '')) == '1':
            step_1 = step
            break
    
    if not step_1:
        print("❌ Step 1 not found in step data")
        return False
    
    # Check URL tracking fields
    stored_url = step_1.get('current_url')
    stored_timestamp = step_1.get('url_capture_timestamp')
    url_history = step_1.get('url_history', [])
    step_execution_urls = step_1.get('step_execution_urls', {})
    
    if not stored_url:
        print("❌ No current_url stored in step data")
        return False
    
    if stored_url != current_url:
        print(f"❌ URL mismatch: expected {current_url}, got {stored_url}")
        return False
    
    print(f"✅ URL correctly stored in JSON:")
    print(f"   📍 Current URL: {stored_url}")
    print(f"   ⏰ Timestamp: {stored_timestamp}")
    print(f"   📚 URL History: {len(url_history)} entries")
    print(f"   🔗 Step Execution URLs: {step_execution_urls}")
    
    print("\n🎉 URL Tracking Integration Test PASSED!")
    print("   ✅ Pytest hooks capture URL data")
    print("   ✅ XML parser preserves URL properties")
    print("   ✅ Stage 7 function extracts URL data")
    print("   ✅ JSON storage persists URL tracking")
    
    return True


if __name__ == "__main__":
    success = test_url_tracking_integration()
    sys.exit(0 if success else 1)
