"""
Real Application Testing Suite

This module tests the state management fixes in a real Streamlit application environment
to validate they work correctly with the full application stack.
"""

import sys
import os
import time
import logging
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from state_manager import StateManager, StateStage
from utils.stage_monitor import get_stage_monitor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("RealApplicationTest")

class MockStreamlit:
    """Enhanced mock Streamlit object for real application testing."""
    def __init__(self):
        self.session_state = {}
        self.rerun_called = False
        self.rerun_count = 0

    def rerun(self):
        self.rerun_called = True
        self.rerun_count += 1

def test_real_stage_1_reversion_prevention():
    """Test the core Stage 1 reversion prevention in a realistic scenario."""
    print("🧪 Testing Stage 1 Reversion Prevention (Real Scenario)")
    print("=" * 60)

    # Simulate a real user workflow
    mock_st = MockStreamlit()

    # Step 1: User uploads file and progresses to Stage 4
    print("Step 1: Simulating user progress to Stage 4...")
    state = StateManager()
    state.init_in_session(mock_st)

    # Simulate file upload
    state.uploaded_excel = "user_test_cases.xlsx"
    state.test_cases = [
        {"Test Case ID": "TC001", "Test Case Name": "Login Test"},
        {"Test Case ID": "TC002", "Test Case Name": "Search Test"}
    ]

    # Progress through stages
    state.advance_to(StateStage.STAGE2_WEBSITE, "File uploaded")
    state.website_url = "https://myapp.com"

    state.advance_to(StateStage.STAGE3_CONVERT, "Website configured")
    state.selected_test_case = {"Test Case ID": "TC001", "Test Case Name": "Login Test"}
    state.conversion_done = True
    state.step_table_json = [
        {"Step No": 1, "Action": "Navigate to login page"},
        {"Step No": 2, "Action": "Enter username"},
        {"Step No": 3, "Action": "Enter password"},
        {"Step No": 4, "Action": "Click login button"}
    ]

    state.advance_to(StateStage.STAGE4_DETECT, "Test case converted")

    print(f"✅ User successfully progressed to: {state.current_stage.get_display_name()}")

    # Step 2: Simulate various triggers that previously caused Stage 1 reversion
    print("\nStep 2: Testing reversion triggers...")

    # Test 2a: Multiple rapid update_stage_based_on_completion calls
    print("  Testing rapid stage update calls...")
    original_stage = state.current_stage

    for i in range(5):
        result = state.update_stage_based_on_completion()
        if state.current_stage == StateStage.STAGE1_UPLOAD:
            print(f"❌ CRITICAL FAILURE: Reverted to Stage 1 on call {i+1}")
            return False

    if state.current_stage == original_stage:
        print("  ✅ Rapid stage update calls: SAFE")
    else:
        print(f"  ⚠️  Stage changed from {original_stage} to {state.current_stage} (unexpected but not Stage 1)")

    # Test 2b: Session state "corruption" simulation
    print("  Testing session state resilience...")

    # Temporarily remove some fields to simulate corruption
    temp_website = state.website_url
    temp_selected = state.selected_test_case

    state.website_url = None
    state.selected_test_case = None

    # This should NOT cause reversion to Stage 1 due to safety mechanisms
    result = state.update_stage_based_on_completion()

    if state.current_stage == StateStage.STAGE1_UPLOAD:
        print("❌ CRITICAL FAILURE: Reverted to Stage 1 due to temporary field corruption")
        return False

    # Restore fields
    state.website_url = temp_website
    state.selected_test_case = temp_selected

    print("  ✅ Session state resilience: SAFE")

    # Test 2c: Navigation attempts
    print("  Testing navigation safety...")

    # Try to navigate to Stage 1 (should be blocked)
    result = state.advance_to(StateStage.STAGE1_UPLOAD, "Manual navigation attempt")

    if result == True or state.current_stage == StateStage.STAGE1_UPLOAD:
        print("❌ CRITICAL FAILURE: Manual Stage 1 navigation was allowed")
        return False

    print("  ✅ Navigation safety: SAFE")

    # Step 3: Verify monitoring system
    print("\nStep 3: Testing monitoring system...")

    monitor = get_stage_monitor()
    recent_transitions = monitor.get_recent_transitions(5)  # Last 5 minutes
    stage1_reversions = monitor.get_stage1_reversions(5)

    print(f"  Recent transitions: {len(recent_transitions)}")
    print(f"  Stage 1 reversions: {len(stage1_reversions)}")

    if len(stage1_reversions) > 0:
        print("❌ CRITICAL FAILURE: Stage 1 reversions detected by monitoring system")
        for reversion in stage1_reversions:
            print(f"    → {reversion.from_stage} -> {reversion.to_stage} at {reversion.timestamp}")
        return False

    print("  ✅ Monitoring system: CLEAN")

    print("\n🎉 ALL REAL APPLICATION TESTS PASSED!")
    print(f"Final stage: {state.current_stage.get_display_name()}")
    return True

def test_state_upgrade_scenario():
    """Test the state upgrade scenario that was causing issues."""
    print("\n🔧 Testing State Upgrade Scenario")
    print("=" * 60)

    # Create a minimal old state object (without current_stage)
    class MinimalOldState:
        def __init__(self):
            self.uploaded_excel = "test.xlsx"
            self.test_cases = [{"Test Case ID": "TC001"}]
            self.website_url = "https://test.com"
            self.selected_test_case = {"Test Case ID": "TC001"}
            self.conversion_done = True
            self.step_table_json = [{"Step No": 1}]
            # NO current_stage field - this is the key

            # Add required fields for upgrade process
            self._script_storage = None

        def _init_script_storage(self):
            """Mock script storage initialization."""
            self._script_storage = "mock_storage"

    mock_st = MockStreamlit()
    old_state = MinimalOldState()

    # Put the old state in session state
    mock_st.session_state["state"] = old_state

    print(f"Old state has current_stage: {hasattr(old_state, 'current_stage')}")

    # Initialize new state manager (this should trigger upgrade)
    new_state = StateManager()
    new_state.init_in_session(mock_st)

    # Get the upgraded state
    upgraded_state = mock_st.session_state["state"]

    print(f"Upgraded state has current_stage: {hasattr(upgraded_state, 'current_stage')}")

    if hasattr(upgraded_state, 'current_stage'):
        print(f"Upgraded to stage: {upgraded_state.current_stage.get_display_name()}")

        # Should be Stage 4 based on the progress
        if upgraded_state.current_stage == StateStage.STAGE4_DETECT:
            print("✅ State upgrade: CORRECT STAGE")
            return True
        elif upgraded_state.current_stage == StateStage.STAGE1_UPLOAD:
            print("❌ CRITICAL FAILURE: Upgraded to Stage 1 (reversion)")
            return False
        else:
            print(f"⚠️  Upgraded to unexpected stage: {upgraded_state.current_stage.get_display_name()}")
            return True  # Not Stage 1, so not a critical failure
    else:
        print("❌ CRITICAL FAILURE: Upgrade did not add current_stage field")
        return False

def test_end_to_end_workflow():
    """Test a complete end-to-end workflow to ensure no regressions."""
    print("\n🔄 Testing End-to-End Workflow")
    print("=" * 60)

    mock_st = MockStreamlit()
    state = StateManager()
    state.init_in_session(mock_st)

    # Complete workflow simulation
    workflow_steps = [
        ("Upload file", lambda: setattr(state, 'uploaded_excel', 'test.xlsx') or setattr(state, 'test_cases', [{"Test Case ID": "TC001"}])),
        ("Configure website", lambda: setattr(state, 'website_url', 'https://test.com')),
        ("Convert test case", lambda: (
            setattr(state, 'selected_test_case', {"Test Case ID": "TC001"}),
            setattr(state, 'conversion_done', True),
            setattr(state, 'step_table_json', [{"Step No": 1}])
        )),
        ("Detect elements", lambda: (
            setattr(state, 'selected_step', {"Step No": 1}),
            setattr(state, 'step_matches', {"element1": "match1"})
        )),
        ("Configure data", lambda: setattr(state, 'test_data', {"username": "test"})),
        ("Generate script", lambda: setattr(state, 'generated_script_path', 'test_script.py')),
        ("Execute script", lambda: setattr(state, 'all_steps_done', True))
    ]

    expected_stages = [
        StateStage.STAGE2_WEBSITE,
        StateStage.STAGE3_CONVERT,
        StateStage.STAGE4_DETECT,
        StateStage.STAGE5_DATA,
        StateStage.STAGE6_GENERATE,
        StateStage.STAGE7_EXECUTE,
        StateStage.STAGE8_OPTIMIZE
    ]

    for i, ((step_name, step_action), expected_stage) in enumerate(zip(workflow_steps, expected_stages)):
        print(f"  Step {i+1}: {step_name}")

        # Perform the step action
        step_action()

        # Advance to the next stage
        result = state.advance_to(expected_stage, step_name)

        if not result:
            print(f"❌ Failed to advance to {expected_stage.get_display_name()}")
            return False

        if state.current_stage != expected_stage:
            print(f"❌ Expected {expected_stage.get_display_name()}, got {state.current_stage.get_display_name()}")
            return False

        # Verify no reversion to Stage 1
        if state.current_stage == StateStage.STAGE1_UPLOAD:
            print(f"❌ CRITICAL FAILURE: Reverted to Stage 1 during step: {step_name}")
            return False

    print("✅ End-to-end workflow: SUCCESSFUL")
    return True

def run_real_application_tests():
    """Run all real application tests."""
    print("🚀 STARTING REAL APPLICATION TESTING")
    print("=" * 80)
    print("Testing GretahAI ScriptWeaver state management fixes in realistic scenarios...")
    print("=" * 80)

    tests = [
        test_real_stage_1_reversion_prevention,
        test_state_upgrade_scenario,
        test_end_to_end_workflow
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"\n❌ {test.__name__} FAILED")
        except Exception as e:
            print(f"\n💥 {test.__name__} CRASHED: {e}")
            import traceback
            traceback.print_exc()

    print("\n" + "=" * 80)
    print("🏁 REAL APPLICATION TEST RESULTS")
    print("=" * 80)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")

    if passed == total:
        print("\n🎉 ALL REAL APPLICATION TESTS PASSED!")
        print("✅ Stage 1 reversion issue is RESOLVED in real application scenarios!")
        return True
    else:
        print(f"\n⚠️  {total-passed} tests failed in real application scenarios.")
        return False

if __name__ == "__main__":
    success = run_real_application_tests()
    sys.exit(0 if success else 1)
