# StateManager Cleanup Implementation Guide
## Step-by-Step Instructions for Code Optimization

---

## Phase 1: Safe Dead Code Removal (Immediate - Low Risk)

### Step 1.1: Remove Deprecated Methods

**Target: Remove ~400 lines of dead code**

```bash
# Methods to remove completely:
# Lines 1557-1640: update_stage_based_on_completion()
# Lines 1642-1653: current_app_stage property
# Lines 2257-2332: get_common_validation_issues()
# Lines 2334-2358: track_script_regeneration()
# Lines 2360-2426: get_feedback_effectiveness_metrics()
# Lines 2218-2255: add_validation_feedback()
```

**Implementation:**
1. Search for any remaining calls to these methods in the codebase
2. Remove the method definitions from StateManager
3. Test application startup and basic functionality
4. Commit changes with message: "Remove deprecated StateManager methods"

### Step 1.2: Simplify Upgrade Logic

**Target: Simplify init_in_session() method**

```python
# Current: Lines 246-287 (complex upgrade logic)
# Target: Simplified version (~30 lines)

def init_in_session(self, st):
    """Initialize the state manager in the Streamlit session state."""
    if "state" not in st.session_state:
        st.session_state["state"] = self
        self._init_script_storage()
    else:
        # Simple compatibility check
        existing_state = st.session_state["state"]
        if not hasattr(existing_state, 'current_stage'):
            existing_state.current_stage = StateStage.STAGE1_UPLOAD
        if not hasattr(existing_state, '_script_storage'):
            existing_state._init_script_storage()
```

---

## Phase 2: Extract Hybrid Editing Module

### Step 2.1: Create core/hybrid_editing.py

```python
"""
Hybrid Editing Manager for GretahAI ScriptWeaver
Handles AI + manual step editing functionality
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
import logging

class HybridEditingManager:
    def __init__(self, state_manager):
        self.state = state_manager
        self.logger = logging.getLogger("ScriptWeaver.hybrid_editing")
    
    def enable_hybrid_editing(self) -> bool:
        """Enable hybrid editing mode for the current test case."""
        if not self.state.step_table_json:
            self.logger.warning("Cannot enable hybrid editing: no step table JSON available")
            return False

        self.state.hybrid_editing_enabled = True
        self.state.ai_generated_steps = self.state.step_table_json.copy()

        # Mark AI steps as locked
        for step in self.state.ai_generated_steps:
            step["_is_ai_generated"] = True
            step["_is_locked"] = True

        self.logger.info("Enabled hybrid editing mode")
        return True
    
    def disable_hybrid_editing(self):
        """Disable hybrid editing mode and clear manual steps."""
        self.state.hybrid_editing_enabled = False
        self.state.step_insertion_points.clear()
        self.state.combined_step_table = None
        self.state.ai_generated_steps = None
        self.logger.info("Disabled hybrid editing mode")
    
    def add_manual_step(self, step: Dict[str, Any], insertion_point: str = "end"):
        """Add a manual step at the specified insertion point."""
        step["_is_manual"] = True
        step["_is_locked"] = False
        step["_insertion_point"] = insertion_point
        step["_created_at"] = datetime.now().isoformat()

        if insertion_point not in self.state.step_insertion_points:
            self.state.step_insertion_points[insertion_point] = []

        self.state.step_insertion_points[insertion_point].append(step)
        self.logger.info(f"Added manual step at insertion point: {insertion_point}")
    
    def remove_manual_step(self, step_id: str, insertion_point: str):
        """Remove a manual step from the specified insertion point."""
        if insertion_point in self.state.step_insertion_points:
            self.state.step_insertion_points[insertion_point] = [
                step for step in self.state.step_insertion_points[insertion_point]
                if step.get("_step_id") != step_id
            ]
            self.logger.info(f"Removed manual step {step_id} from insertion point: {insertion_point}")
    
    def get_combined_steps(self) -> Optional[List[Dict[str, Any]]]:
        """Get the combined AI and manual steps."""
        if not self.state.hybrid_editing_enabled or not self.state.ai_generated_steps:
            return None

        from core.step_merger import merge_ai_and_manual_steps

        merged_steps, _, _ = merge_ai_and_manual_steps(
            self.state.ai_generated_steps,
            self.state.step_insertion_points
        )

        return merged_steps
```

### Step 2.2: Update StateManager Integration

```python
# Add to StateManager class:
@property
def hybrid_editing(self):
    """Access hybrid editing functionality."""
    if not hasattr(self, '_hybrid_editing'):
        from core.hybrid_editing import HybridEditingManager
        self._hybrid_editing = HybridEditingManager(self)
    return self._hybrid_editing

# Replace existing methods with delegation:
def enable_hybrid_editing(self) -> bool:
    return self.hybrid_editing.enable_hybrid_editing()

def disable_hybrid_editing(self):
    return self.hybrid_editing.disable_hybrid_editing()

def add_manual_step(self, step: Dict[str, Any], insertion_point: str = "end"):
    return self.hybrid_editing.add_manual_step(step, insertion_point)

def remove_manual_step(self, step_id: str, insertion_point: str):
    return self.hybrid_editing.remove_manual_step(step_id, insertion_point)

def get_combined_steps(self) -> Optional[List[Dict[str, Any]]]:
    return self.hybrid_editing.get_combined_steps()
```

---

## Phase 3: Extract Data Persistence Module

### Step 3.1: Create core/step_data_persistence.py

```python
"""
Step Data Persistence Manager for GretahAI ScriptWeaver
Handles JSON-based step data storage and retrieval
"""

from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import logging

class StepDataPersistenceManager:
    def __init__(self, state_manager):
        self.state = state_manager
        self.logger = logging.getLogger("ScriptWeaver.step_data_persistence")
    
    def save_to_json(self, step_data: List[Dict[str, Any]], metadata: Dict[str, Any] = None) -> bool:
        """Save step data to persistent JSON storage."""
        if not hasattr(self.state, 'selected_test_case') or not self.state.selected_test_case:
            self.logger.warning("Cannot save step data: no test case selected")
            return False

        test_case_id = self.state.selected_test_case.get('Test Case ID')
        if not test_case_id:
            self.logger.warning("Cannot save step data: test case has no ID")
            return False

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()

            # Prepare metadata
            save_metadata = metadata or {}
            save_metadata.update({
                'test_case_objective': self.state.selected_test_case.get('Test Case Objective', ''),
                'hybrid_editing_enabled': self.state.hybrid_editing_enabled,
                'has_manual_steps': bool(self.state.step_insertion_points),
                'save_timestamp': datetime.now().isoformat()
            })

            success = storage.save_step_data(test_case_id, step_data, save_metadata)

            if success:
                self.logger.info(f"Successfully saved step data to JSON for test case: {test_case_id}")
                self.logger.info(f"  → Step count: {len(step_data)}")
            else:
                self.logger.error(f"Failed to save step data to JSON for test case: {test_case_id}")

            return success

        except Exception as e:
            self.logger.error(f"Error saving step data to JSON: {e}")
            return False
    
    def load_from_json(self) -> Optional[Tuple[List[Dict[str, Any]], Dict[str, Any]]]:
        """Load step data from persistent JSON storage."""
        if not hasattr(self.state, 'selected_test_case') or not self.state.selected_test_case:
            self.logger.warning("Cannot load step data: no test case selected")
            return None

        test_case_id = self.state.selected_test_case.get('Test Case ID')
        if not test_case_id:
            self.logger.warning("Cannot load step data: test case has no ID")
            return None

        try:
            from core.step_data_storage import get_step_data_storage
            storage = get_step_data_storage()

            result = storage.load_step_data(test_case_id)

            if result:
                step_data, metadata = result
                self.logger.info(f"Successfully loaded step data from JSON for test case: {test_case_id}")
                self.logger.info(f"  → Step count: {len(step_data)}")
            else:
                self.logger.info(f"No step data found in JSON for test case: {test_case_id}")

            return result

        except Exception as e:
            self.logger.error(f"Error loading step data from JSON: {e}")
            return None
```

---

## Phase 4: Testing Strategy

### Unit Tests for Extracted Modules

```python
# tests/test_hybrid_editing.py
def test_enable_hybrid_editing():
    state = create_mock_state()
    hybrid_manager = HybridEditingManager(state)
    
    result = hybrid_manager.enable_hybrid_editing()
    assert result == True
    assert state.hybrid_editing_enabled == True

# tests/test_step_data_persistence.py
def test_save_to_json():
    state = create_mock_state()
    persistence_manager = StepDataPersistenceManager(state)
    
    step_data = [{"step_no": 1, "action": "test"}]
    result = persistence_manager.save_to_json(step_data)
    assert result == True
```

### Integration Tests

```python
# tests/test_state_manager_integration.py
def test_hybrid_editing_integration():
    state = StateManager()
    
    # Test property access
    hybrid_manager = state.hybrid_editing
    assert isinstance(hybrid_manager, HybridEditingManager)
    
    # Test method delegation
    result = state.enable_hybrid_editing()
    assert result == True
```

---

## Implementation Checklist

### Phase 1 (Week 1):
- [ ] Remove deprecated methods
- [ ] Simplify init_in_session()
- [ ] Test application startup
- [ ] Verify no broken imports
- [ ] Commit changes

### Phase 2 (Week 2):
- [ ] Create core/hybrid_editing.py
- [ ] Extract hybrid editing methods
- [ ] Update StateManager integration
- [ ] Test hybrid editing functionality
- [ ] Update UI components that use hybrid editing

### Phase 3 (Week 3):
- [ ] Create core/step_data_persistence.py
- [ ] Extract data persistence methods
- [ ] Update StateManager integration
- [ ] Test JSON data operations
- [ ] Verify data consistency

### Phase 4 (Week 4):
- [ ] Create unit tests for extracted modules
- [ ] Create integration tests
- [ ] Performance testing
- [ ] Documentation updates
- [ ] Final cleanup and optimization

---

## Risk Mitigation

1. **Backup Strategy**: Create git branch before each phase
2. **Incremental Testing**: Test after each method extraction
3. **Rollback Plan**: Keep original methods as deprecated until testing complete
4. **User Communication**: Notify users of potential breaking changes
5. **Monitoring**: Add logging to track any issues during transition
