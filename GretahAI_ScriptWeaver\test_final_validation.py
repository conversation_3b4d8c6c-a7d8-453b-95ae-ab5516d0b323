"""
Final Validation Test Suite

This module provides the final validation that the Stage 1 reversion issue is completely resolved.
It focuses on the core problem scenarios that were causing user frustration.
"""

import sys
import os
import logging
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from state_manager import StateManager, StateStage
from utils.stage_monitor import get_stage_monitor

# Configure logging
logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("FinalValidation")

def test_core_stage1_reversion_prevention():
    """Test the core Stage 1 reversion prevention scenarios."""
    print("🎯 FINAL VALIDATION: Stage 1 Reversion Prevention")
    print("=" * 60)
    
    # Test 1: User with significant progress cannot accidentally revert to Stage 1
    print("Test 1: Preventing accidental Stage 1 reversion...")
    
    state = StateManager()
    
    # Simulate user with significant progress
    state.uploaded_excel = "user_test_cases.xlsx"
    state.test_cases = [{"Test Case ID": "TC001", "Test Case Name": "Login Test"}]
    state.website_url = "https://myapp.com"
    state.selected_test_case = {"Test Case ID": "TC001"}
    state.conversion_done = True
    state.step_table_json = [{"Step No": 1, "Action": "Navigate to login page"}]
    state.current_stage = StateStage.STAGE4_DETECT
    
    # Try to navigate to Stage 1 (should be blocked)
    result = state.advance_to(StateStage.STAGE1_UPLOAD, "Accidental navigation attempt")
    
    if result == False and state.current_stage == StateStage.STAGE4_DETECT:
        print("  ✅ PASS: Accidental Stage 1 navigation blocked")
    else:
        print("  ❌ FAIL: Accidental Stage 1 navigation was allowed")
        return False
    
    # Test 2: Multiple rapid update_stage_based_on_completion calls don't cause reversion
    print("Test 2: Preventing rapid update reversion...")
    
    original_stage = state.current_stage
    for i in range(10):
        state.update_stage_based_on_completion()
        if state.current_stage == StateStage.STAGE1_UPLOAD:
            print(f"  ❌ FAIL: Reverted to Stage 1 on rapid update call {i+1}")
            return False
    
    if state.current_stage == original_stage:
        print("  ✅ PASS: Rapid updates did not cause reversion")
    else:
        print(f"  ⚠️  Stage changed from {original_stage} to {state.current_stage} (not Stage 1, so acceptable)")
    
    # Test 3: Session state corruption resilience
    print("Test 3: Session state corruption resilience...")
    
    # Temporarily corrupt some state
    temp_website = state.website_url
    state.website_url = None
    
    # This should NOT cause reversion due to safety mechanisms
    state.update_stage_based_on_completion()
    
    if state.current_stage == StateStage.STAGE1_UPLOAD:
        print("  ❌ FAIL: Reverted to Stage 1 due to temporary corruption")
        return False
    
    # Restore state
    state.website_url = temp_website
    print("  ✅ PASS: Resilient to temporary state corruption")
    
    # Test 4: Monitoring system detects no Stage 1 reversions
    print("Test 4: Monitoring system validation...")
    
    monitor = get_stage_monitor()
    stage1_reversions = monitor.get_stage1_reversions(5)  # Last 5 minutes
    
    if len(stage1_reversions) == 0:
        print("  ✅ PASS: No Stage 1 reversions detected by monitoring system")
    else:
        print(f"  ❌ FAIL: {len(stage1_reversions)} Stage 1 reversions detected")
        for reversion in stage1_reversions:
            print(f"    → {reversion.from_stage} -> {reversion.to_stage}")
        return False
    
    print("\n🎉 ALL CORE TESTS PASSED!")
    return True

def test_legitimate_stage1_access():
    """Test that legitimate Stage 1 access still works when appropriate."""
    print("\n🔓 VALIDATION: Legitimate Stage 1 Access")
    print("=" * 60)
    
    # Test 1: Force reset method works
    print("Test 1: Force reset method...")
    
    state = StateManager()
    state.uploaded_excel = "test.xlsx"
    state.test_cases = [{"Test Case ID": "TC001"}]
    state.current_stage = StateStage.STAGE4_DETECT
    
    # Use force reset method
    result = state.force_reset_to_stage1("User requested complete reset")
    
    if result and state.current_stage == StateStage.STAGE1_UPLOAD:
        print("  ✅ PASS: Force reset method works correctly")
    else:
        print("  ❌ FAIL: Force reset method failed")
        return False
    
    # Test 2: Fresh state starts at Stage 1
    print("Test 2: Fresh state initialization...")
    
    fresh_state = StateManager()
    
    if fresh_state.current_stage == StateStage.STAGE1_UPLOAD:
        print("  ✅ PASS: Fresh state correctly starts at Stage 1")
    else:
        print(f"  ❌ FAIL: Fresh state started at {fresh_state.current_stage}")
        return False
    
    # Test 3: State with no progress can go to Stage 1
    print("Test 3: No progress state can access Stage 1...")
    
    empty_state = StateManager()
    empty_state.current_stage = StateStage.STAGE2_WEBSITE  # Artificially set to Stage 2
    
    # Should be able to go to Stage 1 since there's no progress
    result = empty_state.advance_to(StateStage.STAGE1_UPLOAD, "No progress navigation")
    
    if result and empty_state.current_stage == StateStage.STAGE1_UPLOAD:
        print("  ✅ PASS: No progress state can access Stage 1")
    else:
        print("  ❌ FAIL: No progress state blocked from Stage 1")
        return False
    
    print("\n✅ ALL LEGITIMATE ACCESS TESTS PASSED!")
    return True

def test_workflow_integrity():
    """Test that normal workflow progression still works perfectly."""
    print("\n🔄 VALIDATION: Workflow Integrity")
    print("=" * 60)
    
    state = StateManager()
    
    # Simulate normal user workflow
    workflow_steps = [
        ("Upload file", StateStage.STAGE2_WEBSITE, lambda: (
            setattr(state, 'uploaded_excel', 'test.xlsx'),
            setattr(state, 'test_cases', [{"Test Case ID": "TC001"}])
        )),
        ("Configure website", StateStage.STAGE3_CONVERT, lambda: 
            setattr(state, 'website_url', 'https://test.com')),
        ("Convert test case", StateStage.STAGE4_DETECT, lambda: (
            setattr(state, 'selected_test_case', {"Test Case ID": "TC001"}),
            setattr(state, 'conversion_done', True),
            setattr(state, 'step_table_json', [{"Step No": 1}])
        )),
        ("Detect elements", StateStage.STAGE5_DATA, lambda: (
            setattr(state, 'selected_step', {"Step No": 1}),
            setattr(state, 'step_matches', {"element1": "match1"})
        )),
        ("Configure data", StateStage.STAGE6_GENERATE, lambda: 
            setattr(state, 'test_data', {"username": "test"})),
        ("Generate script", StateStage.STAGE7_EXECUTE, lambda: 
            setattr(state, 'generated_script_path', 'test_script.py')),
        ("Execute script", StateStage.STAGE8_OPTIMIZE, lambda: 
            setattr(state, 'all_steps_done', True))
    ]
    
    for i, (step_name, target_stage, setup_action) in enumerate(workflow_steps):
        print(f"  Step {i+1}: {step_name}")
        
        # Set up the step
        setup_action()
        
        # Advance to the target stage
        result = state.advance_to(target_stage, step_name)
        
        if not result or state.current_stage != target_stage:
            print(f"    ❌ FAIL: Could not advance to {target_stage.get_display_name()}")
            return False
        
        # Verify no accidental reversion to Stage 1
        if state.current_stage == StateStage.STAGE1_UPLOAD and target_stage != StateStage.STAGE1_UPLOAD:
            print(f"    ❌ FAIL: Unexpected reversion to Stage 1 during {step_name}")
            return False
        
        print(f"    ✅ Successfully advanced to {state.current_stage.get_display_name()}")
    
    print("\n✅ WORKFLOW INTEGRITY MAINTAINED!")
    return True

def run_final_validation():
    """Run the final validation test suite."""
    print("🏆 FINAL VALIDATION TEST SUITE")
    print("=" * 80)
    print("Validating that the Stage 1 reversion issue is completely resolved...")
    print("=" * 80)
    
    tests = [
        test_core_stage1_reversion_prevention,
        test_legitimate_stage1_access,
        test_workflow_integrity
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"\n❌ {test.__name__} FAILED")
        except Exception as e:
            print(f"\n💥 {test.__name__} CRASHED: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "=" * 80)
    print("🏁 FINAL VALIDATION RESULTS")
    print("=" * 80)
    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")
    
    if passed == total:
        print("\n🎉 FINAL VALIDATION SUCCESSFUL!")
        print("✅ The Stage 1 reversion issue is COMPLETELY RESOLVED!")
        print("✅ User workflow integrity is maintained!")
        print("✅ Safety mechanisms are working correctly!")
        print("✅ Legitimate Stage 1 access is preserved!")
        print("\n🚀 GretahAI ScriptWeaver is ready for production use!")
        return True
    else:
        print(f"\n⚠️  {total-passed} validation tests failed.")
        print("❌ Stage 1 reversion issue may not be fully resolved.")
        return False

if __name__ == "__main__":
    success = run_final_validation()
    sys.exit(0 if success else 1)
