#!/usr/bin/env python3
"""
Test Stage 7 URL Tracking Fix

This script simulates the Stage 7 workflow with the fix applied to verify
that URL tracking now works correctly in the real application flow.
"""

import sys
import os
import subprocess
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from core.junit_parser import parse_junit_xml, format_test_results_for_display
from stages.stage7 import _capture_and_store_url_tracking
from state_manager import StateManager
from core.step_data_storage import get_step_data_storage


def test_stage7_url_tracking_fix():
    """Test the Stage 7 URL tracking fix by simulating the real workflow."""
    print("🔧 Testing Stage 7 URL Tracking Fix...")
    
    # Step 1: Run a test to generate XML with URL tracking data
    print("📋 Step 1: Running pytest to generate XML results...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    result_xml_path = f"test_stage7_fix_{timestamp}.xml"
    
    pytest_command = [
        "pytest",
        "generated_tests/test_TC_001_1_1748642233_merged.py::test_step1_navigate",
        f"--junitxml={result_xml_path}",
        "-v"
    ]
    
    try:
        result = subprocess.run(
            pytest_command,
            capture_output=True, text=True,
            cwd=os.getcwd()
        )
        
        if result.returncode != 0:
            print(f"❌ Pytest execution failed: {result.stderr}")
            return False
            
        print(f"✅ Pytest completed successfully, XML saved to: {result_xml_path}")
        
    except Exception as e:
        print(f"❌ Error running pytest: {e}")
        return False
    
    # Step 2: Parse XML results (simulating Stage 7 workflow)
    print("📋 Step 2: Parsing XML results...")
    
    xml_results = parse_junit_xml(result_xml_path)
    if not xml_results:
        print("❌ Failed to parse XML results")
        return False
    
    print("✅ XML results parsed successfully")
    
    # Step 3: Format results for display (this is the key fix)
    print("📋 Step 3: Formatting results for display...")
    
    formatted_results = format_test_results_for_display(xml_results)
    if not formatted_results:
        print("❌ Failed to format results")
        return False
    
    print("✅ Results formatted successfully")
    
    # Step 4: Check if test_details with properties exist
    test_details = formatted_results.get('test_details', [])
    if not test_details:
        print("❌ No test_details found in formatted results")
        return False
    
    properties = test_details[0].get('properties', {})
    current_url = properties.get('current_url')
    
    if not current_url:
        print("❌ No current_url found in properties")
        print(f"Available properties: {list(properties.keys())}")
        return False
    
    print(f"✅ URL found in formatted results: {current_url}")
    
    # Step 5: Test the fixed Stage 7 URL tracking function
    print("📋 Step 5: Testing Stage 7 URL tracking function with fix...")
    
    # Create a mock state manager
    state = StateManager()
    state.selected_test_case = {'Test Case ID': 'TC_001'}
    state.selected_step = {'Step No': '1'}
    
    # Call the URL tracking function with formatted_results (the fix)
    try:
        _capture_and_store_url_tracking(state, formatted_results)
        print("✅ URL tracking function executed successfully with formatted results")
    except Exception as e:
        print(f"❌ URL tracking function failed: {e}")
        return False
    
    # Step 6: Verify URL data was stored in JSON
    print("📋 Step 6: Verifying URL data was stored in JSON...")
    
    storage = get_step_data_storage()
    step_data_result = storage.load_step_data('TC_001')
    
    if not step_data_result:
        print("❌ Failed to load step data")
        return False
    
    step_data, metadata = step_data_result
    
    # Find step 1
    step_1 = None
    for step in step_data:
        if str(step.get('step_no', '')) == '1':
            step_1 = step
            break
    
    if not step_1:
        print("❌ Step 1 not found")
        return False
    
    # Check URL tracking fields
    stored_url = step_1.get('current_url')
    stored_timestamp = step_1.get('url_capture_timestamp')
    url_history = step_1.get('url_history', [])
    
    if not stored_url:
        print("❌ No URL stored in step data")
        return False
    
    if stored_url != current_url:
        print(f"❌ URL mismatch: expected {current_url}, got {stored_url}")
        return False
    
    print(f"✅ URL correctly stored in JSON: {stored_url}")
    print(f"✅ Timestamp: {stored_timestamp}")
    print(f"✅ History entries: {len(url_history)}")
    
    # Cleanup
    try:
        os.remove(result_xml_path)
        print(f"🧹 Cleaned up test file: {result_xml_path}")
    except:
        pass
    
    print("\n🎉 Stage 7 URL Tracking Fix Test PASSED!")
    print("   ✅ XML results parsed correctly")
    print("   ✅ Results formatted with test_details")
    print("   ✅ URL tracking function works with formatted results")
    print("   ✅ URL data stored in JSON successfully")
    print("\n🔧 The fix is working! URL tracking should now work in Stage 7.")
    
    return True


if __name__ == "__main__":
    success = test_stage7_url_tracking_fix()
    sys.exit(0 if success else 1)
