"""
Stage Stability Test Suite

This script tests the stage management fixes to ensure they prevent unexpected Stage 1 reversions.

CRITICAL FIX: Test suite to validate the state regression fixes.
"""

import sys
import os
import logging
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from state_manager import StateManager, StateStage
from utils.stage_monitor import get_stage_monitor, record_stage_transition

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("StageStabilityTest")


class MockStreamlit:
    """Mock Streamlit object for testing."""
    def __init__(self):
        self.session_state = {}


def test_stage_determination():
    """Test the new stage determination logic."""
    print("\n=== Testing Stage Determination Logic ===")

    state = StateManager()

    # Test Stage 1 (no progress)
    determined_stage = state._determine_stage_from_state(state)
    assert determined_stage == StateStage.STAGE1_UPLOAD, f"Expected Stage 1, got {determined_stage}"
    print("✅ Stage 1 determination: PASS")

    # Test Stage 2 (file uploaded)
    state.uploaded_excel = "test.xlsx"
    state.test_cases = [{"Test Case ID": "TC001"}]
    determined_stage = state._determine_stage_from_state(state)
    assert determined_stage == StateStage.STAGE2_WEBSITE, f"Expected Stage 2, got {determined_stage}"
    print("✅ Stage 2 determination: PASS")

    # Test Stage 3 (website configured)
    state.website_url = "https://test.com"
    determined_stage = state._determine_stage_from_state(state)
    assert determined_stage == StateStage.STAGE3_CONVERT, f"Expected Stage 3, got {determined_stage}"
    print("✅ Stage 3 determination: PASS")

    # Test Stage 4 (test case converted)
    state.selected_test_case = {"Test Case ID": "TC001"}
    state.conversion_done = True
    state.step_table_json = [{"Step No": 1}]
    determined_stage = state._determine_stage_from_state(state)
    assert determined_stage == StateStage.STAGE4_DETECT, f"Expected Stage 4, got {determined_stage}"
    print("✅ Stage 4 determination: PASS")

    print("✅ All stage determination tests PASSED")


def test_stage_upgrade_logic():
    """Test the state upgrade logic doesn't cause Stage 1 reversion."""
    print("\n=== Testing State Upgrade Logic ===")

    # Create a mock Streamlit object
    mock_st = MockStreamlit()

    # Create a mock "old" state object that simulates a state without current_stage
    class OldStateManager:
        def __init__(self):
            self.uploaded_excel = "test.xlsx"
            self.test_cases = [{"Test Case ID": "TC001"}]
            self.website_url = "https://test.com"
            self.selected_test_case = {"Test Case ID": "TC001"}
            self.conversion_done = True
            self.step_table_json = [{"Step No": 1}]
            # Intentionally NO current_stage field

            # Add minimal required fields to prevent upgrade errors
            self.script_manually_edited = False
            self.original_ai_script_content = ""
            self.manual_edit_timestamp = None
            self.script_edit_mode = False
            self.script_history = []
            self.script_metadata = {}
            self._script_storage = None

    # Create the old state
    existing_state = OldStateManager()

    print(f"DEBUG: existing_state has current_stage: {hasattr(existing_state, 'current_stage')}")

    # Simulate the state being in session state (without current_stage)
    mock_st.session_state["state"] = existing_state

    # Create a new StateManager and initialize it
    new_state = StateManager()
    new_state.init_in_session(mock_st)

    # Get the upgraded state
    upgraded_state = mock_st.session_state["state"]

    # Verify it has current_stage
    assert hasattr(upgraded_state, 'current_stage'), "Upgraded state missing current_stage"

    # Debug: Print the state details
    print(f"DEBUG: Upgraded state current_stage: {upgraded_state.current_stage}")
    print(f"DEBUG: uploaded_excel: {getattr(upgraded_state, 'uploaded_excel', None)}")
    print(f"DEBUG: test_cases: {getattr(upgraded_state, 'test_cases', None)}")
    print(f"DEBUG: website_url: {getattr(upgraded_state, 'website_url', None)}")
    print(f"DEBUG: selected_test_case: {getattr(upgraded_state, 'selected_test_case', None)}")
    print(f"DEBUG: conversion_done: {getattr(upgraded_state, 'conversion_done', None)}")
    print(f"DEBUG: step_table_json: {getattr(upgraded_state, 'step_table_json', None)}")

    # Verify it's not Stage 1 (should be Stage 4 based on progress)
    assert upgraded_state.current_stage != StateStage.STAGE1_UPLOAD, \
        f"Unexpected Stage 1 reversion! Got: {upgraded_state.current_stage}"

    expected_stage = StateStage.STAGE4_DETECT
    assert upgraded_state.current_stage == expected_stage, \
        f"Expected {expected_stage}, got {upgraded_state.current_stage}"

    print("✅ State upgrade logic: PASS")


def test_update_stage_safety():
    """Test the update_stage_based_on_completion safety mechanisms."""
    print("\n=== Testing Update Stage Safety Mechanisms ===")

    state = StateManager()

    # Set up state with significant progress
    state.uploaded_excel = "test.xlsx"
    state.test_cases = [{"Test Case ID": "TC001"}]
    state.website_url = "https://test.com"
    state.selected_test_case = {"Test Case ID": "TC001"}
    state.conversion_done = True
    state.step_table_json = [{"Step No": 1}]
    state.current_stage = StateStage.STAGE4_DETECT

    # Test that advance_to doesn't allow invalid reversions to Stage 1
    original_stage = state.current_stage
    result = state.advance_to(StateStage.STAGE1_UPLOAD, "Test reversion attempt")

    # Should not revert to Stage 1 when progress exists
    assert result == False, "Stage 1 reversion was not blocked!"
    assert state.current_stage == original_stage, \
        f"Unexpected stage change! Original: {original_stage}, Current: {state.current_stage}"

    print("✅ Update stage safety: PASS")

    # Test legal forward transition
    result = state.advance_to(StateStage.STAGE4_DETECT, "Legal forward transition")
    assert result == True, "Legal forward transition was blocked!"
    assert state.current_stage == StateStage.STAGE4_DETECT, "Legal transition failed!"

    print("✅ Rapid update prevention: PASS")


def test_stage_transition_monitoring():
    """Test the stage transition monitoring system."""
    print("\n=== Testing Stage Transition Monitoring ===")

    monitor = get_stage_monitor()

    # Clear any existing transitions
    monitor.transitions.clear()

    # Record some transitions
    record_stage_transition(StateStage.STAGE1_UPLOAD, StateStage.STAGE2_WEBSITE, "Test transition 1")
    record_stage_transition(StateStage.STAGE2_WEBSITE, StateStage.STAGE3_CONVERT, "Test transition 2")
    record_stage_transition(StateStage.STAGE3_CONVERT, StateStage.STAGE1_UPLOAD, "Problematic reversion")

    # Check that transitions were recorded
    assert len(monitor.transitions) == 3, f"Expected 3 transitions, got {len(monitor.transitions)}"

    # Check for Stage 1 reversions
    reversions = monitor.get_stage1_reversions(10)
    assert len(reversions) == 1, f"Expected 1 reversion, got {len(reversions)}"
    assert reversions[0].reason == "Problematic reversion", "Reversion reason not recorded correctly"

    print("✅ Stage transition monitoring: PASS")


def test_advance_to_safety():
    """Test the advance_to method safety mechanisms."""
    print("\n=== Testing advance_to Safety Mechanisms ===")

    state = StateManager()

    # Set up state with progress
    state.uploaded_excel = "test.xlsx"
    state.test_cases = [{"Test Case ID": "TC001"}]
    state.website_url = "https://test.com"
    state.current_stage = StateStage.STAGE3_CONVERT

    # Try to advance to Stage 1 (should be blocked)
    result = state.advance_to(StateStage.STAGE1_UPLOAD, "Test reversion attempt")

    # Should be blocked due to safety checks
    assert result == False, "Stage 1 reversion was not blocked!"
    assert state.current_stage == StateStage.STAGE3_CONVERT, "Stage was changed despite blocking!"

    print("✅ advance_to safety mechanisms: PASS")

    # Test legal forward transition
    result = state.advance_to(StateStage.STAGE4_DETECT, "Legal forward transition")
    assert result == True, "Legal forward transition was blocked!"
    assert state.current_stage == StateStage.STAGE4_DETECT, "Legal transition failed!"

    print("✅ Legal transitions: PASS")


def run_all_tests():
    """Run all stage stability tests."""
    print("🧪 Starting Stage Stability Test Suite")
    print("=" * 50)

    try:
        test_stage_determination()
        test_stage_upgrade_logic()
        test_update_stage_safety()
        test_stage_transition_monitoring()
        test_advance_to_safety()

        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED! Stage stability fixes are working correctly.")
        print("✅ The Stage 1 reversion issue should now be resolved.")

    except AssertionError as e:
        print(f"\n❌ TEST FAILED: {e}")
        print("🚨 Stage stability issues detected!")
        return False
    except Exception as e:
        print(f"\n💥 UNEXPECTED ERROR: {e}")
        return False

    return True


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
