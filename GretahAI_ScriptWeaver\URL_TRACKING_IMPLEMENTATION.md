# URL Tracking Implementation for GretahAI ScriptWeaver

## Overview

This document describes the URL tracking functionality implemented in GretahAI ScriptWeaver to capture page navigation during test execution. The system tracks browser URLs at each step completion and stores this information in the JSON step data storage files for debugging, analysis, and proper page state management.

## Implementation Details

### 1. JSON Step Data Structure Enhancement

Each step entry in the JSON step data storage now includes the following URL tracking fields:

```json
{
  "step_no": "1",
  "action": "navigate",
  "step_description": "Navigate to the login page",

  // URL Tracking Fields
  "current_url": "https://demo.testsite.com/login",
  "url_capture_timestamp": "2025-05-30T14:33:46.923755",
  "url_history": [
    {
      "url": "https://demo.testsite.com/login",
      "timestamp": "2025-05-30T14:33:46.922754",
      "step_no": "1",
      "action": "navigate",
      "method": "browser.get()"
    }
  ],
  "step_execution_urls": {
    "start_url": null,
    "end_url": "https://demo.testsite.com/login",
    "intermediate_urls": [],
    "navigation_method": "direct"
  }
}
```

#### Field Descriptions:

- **`current_url`**: The current browser URL after step completion
- **`url_capture_timestamp`**: Timestamp when the URL was captured
- **`url_history`**: Array of URL entries with detailed context
- **`step_execution_urls`**: Object tracking URL changes during step execution
  - `start_url`: URL at the beginning of step execution
  - `end_url`: URL at the end of step execution
  - `intermediate_urls`: Array of URLs visited during step execution
  - `navigation_method`: How navigation occurred (direct, redirect, none)

### 2. Core Components Modified

#### A. Step Data Storage (`core/step_data_storage.py`)

**New Methods Added:**
- `update_step_url_tracking(test_case_id, step_no, url_data)`: Updates URL tracking for a specific step
- `get_step_url_history(test_case_id, step_no=None)`: Retrieves URL history for steps
- Enhanced `save_step_data()`: Automatically initializes URL tracking fields

#### B. Pytest Configuration (`conftest.py`)

**Enhanced pytest hook:**
- `pytest_runtest_makereport`: Now captures current browser URL and timestamp
- Stores URL information in test user_properties for later retrieval

#### C. Stage 7 Test Execution (`stages/stage7.py`)

**New Integration:**
- `_capture_and_store_url_tracking()`: Extracts URL data from pytest results and updates step data
- Integrated into successful test execution flow
- Displays captured URL information in the UI

#### D. StateManager (`state_manager.py`)

**New Methods Added:**
- `update_step_url_tracking(step_no, url_data)`: StateManager wrapper for URL tracking updates
- `get_step_url_history(step_no=None)`: Retrieves URL history through StateManager
- `get_current_step_url()`: Gets current URL for the selected step

### 3. Integration Flow

```mermaid
graph TD
    A[Test Execution Starts] --> B[Pytest Hook Captures URL]
    B --> C[Test Completes Successfully]
    C --> D[_capture_and_store_url_tracking Called]
    D --> E[Extract URL from Pytest Results]
    E --> F[Update Step Data Storage]
    F --> G[Display URL in UI]
    G --> H[URL Persisted in JSON File]
```

### 4. Usage Examples

#### Capturing URL During Test Execution

When a test step completes successfully in Stage 7:

1. **Pytest Hook Captures URL:**
   ```python
   current_url = driver.current_url
   item.user_properties.append(("current_url", current_url))
   ```

2. **Stage 7 Processes Results:**
   ```python
   _capture_and_store_url_tracking(state, xml_results)
   ```

3. **URL Data Stored:**
   ```python
   url_data = {
       'current_url': 'https://example.com/dashboard',
       'url_history': [...],
       'step_execution_urls': {...}
   }
   storage.update_step_url_tracking(test_case_id, step_no, url_data)
   ```

#### Retrieving URL History

```python
# Get URL history for a specific step
url_history = state.get_step_url_history("1")

# Get URL history for all steps
all_url_history = state.get_step_url_history()

# Get current URL for selected step
current_url = state.get_current_step_url()
```

### 5. Benefits

1. **Enhanced Debugging**: See exactly where the browser was when a test failed
2. **Navigation Verification**: Confirm expected page transitions occurred
3. **State Management**: Use previous step URLs as starting points for next steps
4. **Test Analysis**: Maintain complete navigation history for test review
5. **Failure Investigation**: Understand navigation flow leading to failures

### 6. File Storage

URL tracking data is stored in JSON step data storage files using a **single file per test case** approach:

**New Single File Format:**
```
GretahAI_ScriptWeaver/step_data_storage/step_data_TC_XXX.json
```

**Legacy Timestamped Format (backward compatible):**
```
GretahAI_ScriptWeaver/step_data_storage/step_data_TC_XXX_YYYYMMDD_HHMMSS_XXXXXX.json
```

#### Key Storage Benefits:
- **No File Proliferation**: Each test case has exactly one JSON file that gets updated in place
- **Automatic Migration**: Legacy timestamped files are automatically migrated to single file format
- **Atomic Updates**: File operations use temporary files with atomic rename for data integrity
- **Backward Compatibility**: Existing timestamped files continue to work during transition
- **Cleaner Directory**: Predictable filenames make file management easier

The data persists across application restarts and maintains a complete history of URL changes throughout the test execution workflow.

### 7. Integration Fix Applied

**Issue Identified**: URL tracking was not working due to a data structure mismatch between the pytest XML parser and Stage 7 URL extraction logic.

**Root Cause**: The `format_test_results_for_display()` function in `core/junit_parser.py` was not preserving the raw properties from pytest XML output, causing URL tracking data to be lost.

**Fix Applied**:
1. **Enhanced XML Parser**: Modified `core/junit_parser.py` to preserve all properties in both raw and formatted results
2. **Properties Preservation**: Added `properties` field to test details in formatted output
3. **Complete Integration**: Verified end-to-end URL tracking from pytest hooks to step data storage

**Integration Chain Verified**:
```
Pytest Hook → XML Properties → Parser Preservation → Stage 7 Extraction → Step Data Storage
     ✅              ✅               ✅                    ✅                  ✅
```

### 8. Testing

The implementation includes comprehensive tests and verification:

- **Integration Tests**: Verified complete URL tracking chain works correctly
- **XML Parser Tests**: Confirmed properties are preserved through parsing
- **Stage 7 Tests**: Validated URL extraction from formatted results
- **Storage Tests**: Verified URL data is correctly stored in JSON files

All integration tests pass, confirming URL tracking is now fully operational.

### 9. Single File Storage Implementation

The implementation was enhanced to use a single JSON file per test case instead of creating multiple timestamped versions:

#### Previous Behavior (Timestamped Files):
- Created new file for every update: `step_data_TC_001_20250530_144016_864214.json`
- Automatic cleanup kept only 10 most recent versions
- File proliferation led to storage directory clutter
- Each URL tracking update created a new timestamped file

#### New Behavior (Single File):
- One file per test case: `step_data_TC_001.json`
- In-place updates preserve single file approach
- Automatic migration from legacy timestamped files
- Atomic file operations ensure data integrity
- No file proliferation regardless of update frequency

#### Migration Process:
1. **Automatic Detection**: System checks for single file first, falls back to timestamped files
2. **Seamless Migration**: Latest timestamped file data is migrated to single file format
3. **Preserved Data**: All step data, metadata, and URL tracking information is maintained
4. **Backward Compatibility**: Legacy timestamped files remain accessible during transition

### 10. Future Enhancements

Potential future improvements:
- Visual URL timeline in the UI
- URL-based test step validation
- Automatic page state verification
- URL pattern matching for assertions
- Integration with browser navigation history
- Optional cleanup of legacy timestamped files after migration

---

© 2025 Cogniron All Rights Reserved.
