{"test_case_id": "TC_001", "timestamp": "2025-05-30T15:36:19.845229", "data_hash": "100ca169af16c6cfefa0df372e1cefe0d6edac5952b5d8dc72856b670e1cd1ca", "step_count": 4, "metadata": {"source": "real_time_update", "operation": "script_generated_step_1", "update_timestamp": "2025-05-30T15:36:19.845229", "step_count": 4, "step_no": "1", "script_file_path": "generated_tests\\test_TC_001_1_1748644579_merged.py", "script_content_length": 1197, "step_specific_file": "generated_tests\\test_TC_001_1_1748644579_step_only.py", "generation_method": "ai_generated", "test_case_objective": "Verify that a user with valid credentials can successfully log in.", "hybrid_editing_enabled": false, "has_manual_steps": false, "save_timestamp": "2025-05-30T15:36:19.845229"}, "step_data": [{"step_no": "1", "step_type": "ui", "action": "navigate", "locator_strategy": "", "locator": "", "test_data_param": "{{login_url}}", "expected_result": "login_page", "assertion_type": "url_equals", "condition": "", "timeout": 10, "step_description": "Navigate to the login page", "current_url": null, "url_history": [], "url_capture_timestamp": null, "step_execution_urls": {"start_url": null, "end_url": null, "intermediate_urls": []}, "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_1_1748644579_merged.py", "_script_content_length": 1197, "_script_generation_timestamp": "2025-05-30T15:36:19.845229", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_1_1748644579_step_only.py"}, {"step_no": "2", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "userid", "test_data_param": "{{username}}", "expected_result": "userid_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid user ID in the designated field", "current_url": null, "url_history": [], "url_capture_timestamp": null, "step_execution_urls": {"start_url": null, "end_url": null, "intermediate_urls": []}}, {"step_no": "3", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "password", "test_data_param": "{{password}}", "expected_result": "password_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid password in the designated field", "current_url": null, "url_history": [], "url_capture_timestamp": null, "step_execution_urls": {"start_url": null, "end_url": null, "intermediate_urls": []}}, {"step_no": "4", "step_type": "ui", "action": "click", "locator_strategy": "css", "locator": "#login-button", "test_data_param": "", "expected_result": "dashboard_page", "assertion_type": "element_visible", "condition": "", "timeout": 10, "step_description": "Click the login button", "current_url": null, "url_history": [], "url_capture_timestamp": null, "step_execution_urls": {"start_url": null, "end_url": null, "intermediate_urls": []}}]}