{"test_case_id": "TC_001", "timestamp": "2025-05-30T15:21:08.650356", "data_hash": "e8cd4714e1f56c85df95a043fa40e410df62fb286abc5ad09942405cf15c9daa", "step_count": 4, "metadata": {"source": "real_time_update", "operation": "script_generated_step_4", "update_timestamp": "2025-05-30T15:21:08.650356", "step_count": 4, "step_no": "4", "script_file_path": "generated_tests\\test_TC_001_4_1748643668_merged.py", "script_content_length": 3372, "step_specific_file": "generated_tests\\test_TC_001_4_1748643668_step_only.py", "generation_method": "ai_generated", "test_case_objective": "Verify that a user with valid credentials can successfully log in.", "hybrid_editing_enabled": false, "has_manual_steps": false, "save_timestamp": "2025-05-30T15:21:08.650356"}, "step_data": [{"step_no": "1", "step_type": "ui", "action": "navigate", "locator_strategy": "", "locator": "", "test_data_param": "{{login_page_url}}", "expected_result": "login_page", "assertion_type": "url_equals", "condition": "", "timeout": 10, "step_description": "Navigate to the login page", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_151354", "step_no": "1", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-30T15:14:01.275215", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_1_1748643221_merged.py", "_script_content_length": 1249, "_script_generation_timestamp": "2025-05-30T15:13:41.788227", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_1_1748643221_step_only.py"}, {"step_no": "2", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "username", "test_data_param": "{{username}}", "expected_result": "username_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid user ID in the designated field", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_151457", "step_no": "2", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-30T15:15:05.512336", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_test_data": {"manual_input_for_step_2": "tomsmith"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-30T15:14:32.616466", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_2_1748643280_merged.py", "_script_content_length": 1985, "_script_generation_timestamp": "2025-05-30T15:14:40.317538", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_2_1748643280_step_only.py"}, {"step_no": "3", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "password", "test_data_param": "{{password}}", "expected_result": "password_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter a valid password in the designated field", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_151626", "step_no": "3", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-30T15:16:36.837716", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_test_data": {"manual_input_for_step_3": "SuperSecretPassword!"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-30T15:16:05.178497", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_3_1748643373_merged.py", "_script_content_length": 2857, "_script_generation_timestamp": "2025-05-30T15:16:13.736421", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_3_1748643373_step_only.py"}, {"step_no": "4", "step_type": "ui", "action": "click", "locator_strategy": "css", "locator": "#login-button", "test_data_param": "", "expected_result": "dashboard_page", "assertion_type": "element_visible", "condition": "", "timeout": 10, "step_description": "Click the login button", "current_url": null, "url_history": [], "url_capture_timestamp": null, "step_execution_urls": {"start_url": null, "end_url": null, "intermediate_urls": []}, "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_4_1748643668_merged.py", "_script_content_length": 3372, "_script_generation_timestamp": "2025-05-30T15:21:08.650356", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_4_1748643668_step_only.py"}]}